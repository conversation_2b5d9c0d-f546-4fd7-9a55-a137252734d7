<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payslip - April 2025</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 20px;
      color: #2c3e50;
      background-color: #f8f9fa;
      line-height: 1.6;
    }

    .container {
      max-width: 850px;
      margin: auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    /* Header Section with Logo */
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      text-align: center;
      position: relative;
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;
      margin-bottom: 15px;
    }

    .company-logo {
      width: 60px;
      height: 60px;
      background: white;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      color: #667eea;
      font-size: 24px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .company-info h1 {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .company-address {
      font-size: 16px;
      opacity: 0.9;
      font-weight: 300;
    }

    .payslip-title {
      font-size: 20px;
      font-weight: 600;
      margin-top: 15px;
      padding: 10px 20px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      display: inline-block;
    }

    /* Content Section */
    .content {
      padding: 30px;
    }

    .section {
      margin-bottom: 30px;
    }

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 2px solid #667eea;
    }

    /* Employee Information Grid */
    .employee-info {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px 30px;
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border-left: 4px solid #667eea;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .info-label {
      font-weight: 600;
      color: #34495e;
      min-width: 120px;
    }

    .info-value {
      color: #2c3e50;
      font-weight: 500;
    }

    /* Table Styling */
    .table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .table th {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 15px 12px;
      text-align: left;
      font-weight: 600;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .table td {
      padding: 12px;
      border-bottom: 1px solid #e9ecef;
      font-size: 14px;
    }

    .table tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    .table tr:hover {
      background-color: #e3f2fd;
      transition: background-color 0.2s ease;
    }

    .total {
      font-weight: 700;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      color: white !important;
    }

    .total td {
      font-size: 16px;
      padding: 15px 12px;
    }

    /* Net Pay Section */
    .net-pay {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 25px;
      border-radius: 8px;
      text-align: center;
      margin: 20px 0;
    }

    .net-pay-amount {
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 10px;
    }

    .net-pay-words {
      font-size: 16px;
      opacity: 0.9;
      font-style: italic;
    }

    /* Footer */
    .footer {
      background: #f8f9fa;
      padding: 20px;
      text-align: center;
      border-top: 1px solid #e9ecef;
      font-style: italic;
      color: #6c757d;
    }

    /* Print Styles */
    @media print {
      body {
        margin: 0;
        background: white;
      }

      .container {
        box-shadow: none;
        border: 1px solid #ddd;
      }

      .header {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }

      .table th {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }

      .total {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }

      .net-pay {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      body {
        margin: 10px;
      }

      .header-content {
        flex-direction: column;
        gap: 15px;
      }

      .employee-info {
        grid-template-columns: 1fr;
        gap: 10px;
      }

      .content {
        padding: 20px;
      }

      .company-info h1 {
        font-size: 24px;
      }

      .net-pay-amount {
        font-size: 28px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header Section with Logo -->
    <div class="header">
      <div class="header-content">
        <div class="company-logo">
          <!-- Placeholder for company logo - replace with actual icon.png -->
          <img src="icon.png" alt="Company Logo" style="width: 100%; height: 100%; object-fit: contain; border-radius: 8px;"
               onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
          <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; font-size: 18px;">
            NS
          </div>
        </div>
        <div class="company-info">
          <h1>NonStop io Technologies Pvt Ltd</h1>
          <div class="company-address">320, Panchashil SOHO, Kharadi, Pune - 411014</div>
        </div>
      </div>
      <div class="payslip-title">Payslip for the month of April 2025</div>
    </div>

    <!-- Content Section -->
    <div class="content">
      <!-- Employee Information Section -->
      <div class="section">
        <h3 class="section-title">Employee Information</h3>
        <div class="employee-info">
          <div class="info-item">
            <span class="info-label">Name:</span>
            <span class="info-value">Ajay Kumar</span>
          </div>
          <div class="info-item">
            <span class="info-label">Employee No:</span>
            <span class="info-value">104</span>
          </div>
          <div class="info-item">
            <span class="info-label">Joining Date:</span>
            <span class="info-value">01 Aug 2017</span>
          </div>
          <div class="info-item">
            <span class="info-label">Bank Name:</span>
            <span class="info-value">ICICI Bank</span>
          </div>
          <div class="info-item">
            <span class="info-label">Designation:</span>
            <span class="info-value">SDE -III</span>
          </div>
          <div class="info-item">
            <span class="info-label">Bank A/C No:</span>
            <span class="info-value">************</span>
          </div>
          <div class="info-item">
            <span class="info-label">Department:</span>
            <span class="info-value">IT</span>
          </div>
          <div class="info-item">
            <span class="info-label">PAN:</span>
            <span class="info-value">**********</span>
          </div>
          <div class="info-item">
            <span class="info-label">Location:</span>
            <span class="info-value">Pune</span>
          </div>
          <div class="info-item">
            <span class="info-label">PF No:</span>
            <span class="info-value">PU/PUN/2163320/000/0010003</span>
          </div>
          <div class="info-item">
            <span class="info-label">Effective Work Days:</span>
            <span class="info-value">30</span>
          </div>
          <div class="info-item">
            <span class="info-label">LOP:</span>
            <span class="info-value">0</span>
          </div>
          <div class="info-item">
            <span class="info-label">PF UAN:</span>
            <span class="info-value">101625600553</span>
          </div>
        </div>
      </div>

      <!-- Earnings Section -->
      <div class="section">
        <h3 class="section-title">Earnings</h3>
        <table class="table">
          <thead>
            <tr>
              <th>Description</th>
              <th style="text-align: right;">Amount (INR)</th>
            </tr>
          </thead>
          <tbody>
            <tr><td>Basic Salary</td><td style="text-align: right;">₹1,66,667</td></tr>
            <tr><td>House Rent Allowance (HRA)</td><td style="text-align: right;">₹66,667</td></tr>
            <tr><td>Travelling Allowance</td><td style="text-align: right;">₹10,000</td></tr>
            <tr><td>Medical Allowance</td><td style="text-align: right;">₹10,000</td></tr>
            <tr><td>Special Allowance</td><td style="text-align: right;">₹52,000</td></tr>
            <tr><td>Internet Allowance</td><td style="text-align: right;">₹8,000</td></tr>
            <tr><td>Bonus Award</td><td style="text-align: right;">₹25,000</td></tr>
            <tr><td>Employer PF Contribution</td><td style="text-align: right;">₹20,000</td></tr>
            <tr class="total">
              <td>Total Earnings</td>
              <td style="text-align: right;">₹3,58,334</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Deductions Section -->
      <div class="section">
        <h3 class="section-title">Deductions</h3>
        <table class="table">
          <thead>
            <tr>
              <th>Description</th>
              <th style="text-align: right;">Amount (INR)</th>
            </tr>
          </thead>
          <tbody>
            <tr><td>Provident Fund (PF)</td><td style="text-align: right;">₹20,000</td></tr>
            <tr><td>Professional Tax</td><td style="text-align: right;">₹200</td></tr>
            <tr><td>Income Tax (TDS)</td><td style="text-align: right;">₹59,410</td></tr>
            <tr><td>Medical Insurance</td><td style="text-align: right;">₹500</td></tr>
            <tr><td>Employer PF Deduction</td><td style="text-align: right;">₹20,000</td></tr>
            <tr class="total">
              <td>Total Deductions</td>
              <td style="text-align: right;">₹1,00,110</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Net Pay Section -->
      <div class="net-pay">
        <div class="net-pay-amount">Net Pay: ₹2,58,224</div>
        <div class="net-pay-words">(Rupees Two Lakh Fifty Eight Thousand Two Hundred Twenty Four Only)</div>
      </div>
    </div>

    <!-- Footer -->
    <div class="footer">
      <p>This is a system generated payslip and does not require signature.</p>
      <p style="margin-top: 10px; font-size: 12px;">Generated on: April 30, 2025</p>
    </div>
  </div>
</body>
</html>