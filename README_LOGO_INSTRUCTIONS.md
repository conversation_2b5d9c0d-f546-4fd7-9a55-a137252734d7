# Company Logo Setup Instructions

## Adding Your Company Logo

To add your company logo to the payslip, follow these steps:

### 1. Prepare Your Logo File
- **File name**: `icon.png`
- **Recommended size**: 60x60 pixels (or any square dimensions)
- **Format**: PNG with transparent background (recommended)
- **File size**: Keep under 100KB for optimal loading

### 2. Logo Placement
Place your `icon.png` file in the same directory as your `index.html` file.

### 3. Logo Requirements
- **Aspect ratio**: Square (1:1) works best
- **Background**: Transparent or white background
- **Style**: Simple, clean design that works well at small sizes
- **Colors**: Should work well with the blue gradient header background

### 4. Fallback Display
If no `icon.png` file is found, the payslip will display "NS" as a placeholder in a styled box.

### 5. Testing Your Logo
1. Add your `icon.png` file to the directory
2. Open `index.html` in a web browser
3. Check that the logo displays properly in the header
4. Test print preview to ensure it appears correctly when printed

### 6. Logo Design Tips
- Use vector graphics converted to PNG for crisp display
- Ensure the logo is readable at 60px size
- Consider how it looks against the gradient background
- Test in both light and dark printing modes

### Current Payslip Features
✅ Professional gradient header design
✅ Responsive layout for mobile and desktop
✅ Print-friendly styling
✅ Clean typography and spacing
✅ Organized employee information grid
✅ Enhanced table styling with hover effects
✅ Prominent net pay display
✅ Currency formatting with ₹ symbol

### File Structure
```
your-project/
├── index.html          (Enhanced payslip)
├── icon.png           (Your company logo - ADD THIS)
└── README_LOGO_INSTRUCTIONS.md
```

The payslip is now ready for professional use with your company branding!
